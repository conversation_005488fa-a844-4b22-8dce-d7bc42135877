import 'package:flutter/material.dart';
import 'parcourir_screen.dart';

class AccueilScreen extends StatefulWidget {
  const AccueilScreen({super.key});

  @override
  State<AccueilScreen> createState() => _AccueilScreenState();
}

class _AccueilScreenState extends State<AccueilScreen> {
  int _currentIndex = 0;

  // Méthode pour obtenir le titre de l'AppBar selon l'index sélectionné
  String _getAppBarTitle() {
    switch (_currentIndex) {
      case 0:
        return 'Bienvenue';
      case 1:
        return 'Parcourir';
      case 2:
        return 'Mes Cours';
      case 3:
        return 'Instructions';
      default:
        return 'Bienvenue';
    }
  }

  // Méthode pour obtenir le contenu de la page selon l'index sélectionné
  Widget _getPageContent() {
    switch (_currentIndex) {
      case 0:
        return _buildAccueilContent();
      case 1:
        return _buildParcourirContent();
      case 2:
        return _buildMesCoursContent();
      case 3:
        return _buildInstructionsContent();
      default:
        return _buildAccueilContent();
    }
  }

  // Contenu de la page d'accueil
  Widget _buildAccueilContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Card(
        color: Colors.pink[50],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Bonjour, Sarah !',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'Prête à devenir une experte en pâtisserie ?',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Image.asset('assets/images/R.jpg', height: 200, fit: BoxFit.cover),
              const SizedBox(height: 16),
              const Text(
                'Nouveaux Cours de Pâtisserie',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'Créative !',
                style: TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
              const SizedBox(height: 8),
              const Text(
                'Découvrez l\'art de la pâtisserie dès aujourd\'hui avec des bases avancées et des techniques créatives, avec notre équipe renommée.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _currentIndex = 2; // Naviguer vers "Mes Cours"
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.pink[100],
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: const Text('Explorer les Cours de Pâtisserie'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Contenu de la page Parcourir
  Widget _buildParcourirContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Catégories',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.pink[50],
                  side: BorderSide(color: Colors.pink[100]!),
                ),
                child: const Text('Tous les catégories'),
              ),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[50]),
                child: const Text('Cuisine'),
              ),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[50]),
                child: const Text('Couture'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            'Niveaux',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.pink[50],
                  side: BorderSide(color: Colors.pink[100]!),
                ),
                child: const Text('Tous les niveaux'),
              ),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[50]),
                child: const Text('Débutant'),
              ),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[50]),
                child: const Text('Intermédiaire'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            'Explorez les cours',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
              title: const Text('Maîtrise de la Pâtisserie'),
              subtitle: const Text('Chef Amélie Dul'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text('Avancé'),
                  LinearProgressIndicator(value: 0.75),
                  Text('75% terminé'),
                ],
              ),
            ),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
              title: const Text('Techniques de Maquillage'),
              subtitle: const Text('Léa Bernard'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text('Intermédiaire'),
                  LinearProgressIndicator(value: 0.50),
                  Text('50% terminé'),
                ],
              ),
            ),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
              title: const Text('Initiation à la Couture'),
              subtitle: const Text('Sophie Martin'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text('Débutant'),
                  LinearProgressIndicator(value: 0.25),
                  Text('25% terminé'),
                ],
              ),
            ),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
              title: const Text('Anglais des Affaires :'),
              subtitle: const Text('Mme. Emily Wat'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text('Intermédiaire'),
                  LinearProgressIndicator(value: 0.30),
                  Text('30% terminé'),
                ],
              ),
            ),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
              title: const Text('Entrepreneuriat'),
              subtitle: const Text('Les Fondamen'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text('Dr. Claire Laur'),
                  LinearProgressIndicator(value: 0.10),
                  Text('10% terminé'),
                ],
              ),
            ),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/images/A.png', width: 60, fit: BoxFit.cover),
              title: const Text('Introduction au Design'),
              subtitle: const Text('Sarah Dupont'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text('Avancé'),
                  LinearProgressIndicator(value: 0.15),
                  Text('15% terminé'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Contenu de la page Mes Cours
  Widget _buildMesCoursContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.school_rounded, size: 80, color: Colors.pink),
          SizedBox(height: 16),
          Text(
            'Mes Cours',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Vos cours de pâtisserie en cours',
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  // Contenu de la page Instructions
  Widget _buildInstructionsContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.receipt_long_rounded, size: 80, color: Colors.pink),
          SizedBox(height: 16),
          Text(
            'Instructions',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Guides et instructions détaillées',
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getAppBarTitle()),
        backgroundColor: Colors.pink[50],
        elevation: 0,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: CircleAvatar(
              backgroundImage: NetworkImage('https://via.placeholder.com/40?text=User'),
            ),
          ),
        ],
      ),
      body: _getPageContent(),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.white,
        selectedItemColor: Colors.pink[400],
        unselectedItemColor: Colors.grey,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_rounded),
            label: 'Accueil'
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.explore_rounded),
            label: 'Parcourir'
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.school_rounded),
            label: 'Mes Cours'
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.receipt_long_rounded),
            label: 'Instructions'
          ),
        ],
      ),
    );
  }
}