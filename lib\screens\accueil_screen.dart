import 'package:flutter/material.dart';
import 'parcourir_screen.dart';
import 'mes_cours_screen.dart';
import 'instructions_screen.dart';
import 'profil_screen.dart';

class AccueilScreen extends StatefulWidget {
  const AccueilScreen({super.key});

  @override
  State<AccueilScreen> createState() => _AccueilScreenState();
}

class _AccueilScreenState extends State<AccueilScreen> {
  int _currentIndex = 0;

  // Méthode pour obtenir le titre de l'AppBar selon l'index sélectionné
  String _getAppBarTitle() {
    switch (_currentIndex) {
      case 0:
        return 'Bienvenue';
      case 1:
        return 'Parcourir';
      case 2:
        return 'Mes Cours';
      case 3:
        return 'Instructions';
      default:
        return 'Bienvenue';
    }
  }

  // Contenu de la page d'accueil uniquement
  Widget _getPageContent() {
    return _buildAccueilContent();
  }

  // Contenu de la page d'accueil
  Widget _buildAccueilContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Card(
        color: Colors.pink[50],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Bonjour, Sarah !',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'Prête à devenir une experte en pâtisserie ?',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Image.asset('assets/images/R.jpg', height: 200, fit: BoxFit.cover),
              const SizedBox(height: 16),
              const Text(
                'Nouveaux Cours de Pâtisserie',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'Créative !',
                style: TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
              const SizedBox(height: 8),
              const Text(
                'Découvrez l\'art de la pâtisserie dès aujourd\'hui avec des bases avancées et des techniques créatives, avec notre équipe renommée.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _currentIndex = 2; // Naviguer vers "Mes Cours"
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.pink[100],
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: const Text('Explorer les Cours de Pâtisserie'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Contenu de la page Parcourir
  Widget _buildParcourirContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Catégories',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.pink[50],
                  side: BorderSide(color: Colors.pink[100]!),
                ),
                child: const Text('Tous les catégories'),
              ),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[50]),
                child: const Text('Cuisine'),
              ),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[50]),
                child: const Text('Couture'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            'Niveaux',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.pink[50],
                  side: BorderSide(color: Colors.pink[100]!),
                ),
                child: const Text('Tous les niveaux'),
              ),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[50]),
                child: const Text('Débutant'),
              ),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[50]),
                child: const Text('Intermédiaire'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            'Explorez les cours',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
              title: const Text('Maîtrise de la Pâtisserie'),
              subtitle: const Text('Chef Amélie Dul'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text('Avancé'),
                  LinearProgressIndicator(value: 0.75),
                  Text('75% terminé'),
                ],
              ),
            ),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
              title: const Text('Techniques de Maquillage'),
              subtitle: const Text('Léa Bernard'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text('Intermédiaire'),
                  LinearProgressIndicator(value: 0.50),
                  Text('50% terminé'),
                ],
              ),
            ),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
              title: const Text('Initiation à la Couture'),
              subtitle: const Text('Sophie Martin'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text('Débutant'),
                  LinearProgressIndicator(value: 0.25),
                  Text('25% terminé'),
                ],
              ),
            ),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
              title: const Text('Anglais des Affaires :'),
              subtitle: const Text('Mme. Emily Wat'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text('Intermédiaire'),
                  LinearProgressIndicator(value: 0.30),
                  Text('30% terminé'),
                ],
              ),
            ),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
              title: const Text('Entrepreneuriat'),
              subtitle: const Text('Les Fondamen'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text('Dr. Claire Laur'),
                  LinearProgressIndicator(value: 0.10),
                  Text('10% terminé'),
                ],
              ),
            ),
          ),
          Card(
            child: ListTile(
              leading: Image.asset('assets/images/A.png', width: 60, fit: BoxFit.cover),
              title: const Text('Introduction au Design'),
              subtitle: const Text('Sarah Dupont'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text('Avancé'),
                  LinearProgressIndicator(value: 0.15),
                  Text('15% terminé'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Contenu de la page Mes Cours
  Widget _buildMesCoursContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset('assets/images/R.jpg', height: 150, fit: BoxFit.cover),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Maîtriser la Pâtisserie Française : Les Essentiels',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const Text(
                        'Chef Sophie Dubois',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(value: 0.75),
                      const SizedBox(height: 4),
                      const Text('75%'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset('assets/images/A.png', height: 150, fit: BoxFit.cover),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Confectionner des Vêtements Élégants : Niveaux Débutant',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const Text(
                        'Styliste Clara Martin',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(value: 0.50),
                      const SizedBox(height: 4),
                      const Text('50%'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Contenu de la page Instructions
  Widget _buildInstructionsContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const Text(
            'Nos Instructrices Experts',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Card(
            child: ListTile(
              leading: const CircleAvatar(
                backgroundImage: NetworkImage('https://via.placeholder.com/50?text=Sophie'),
              ),
              title: const Text('Sophie Dubois'),
              subtitle: const Text('Pâtisserie Expert'),
              trailing: const Icon(Icons.arrow_forward_ios),
            ),
          ),
          Card(
            child: ListTile(
              leading: const CircleAvatar(
                backgroundImage: NetworkImage('https://via.placeholder.com/50?text=Lisa'),
              ),
              title: const Text('Lisa Bernard'),
              subtitle: const Text('Couture Expert'),
              trailing: const Icon(Icons.arrow_forward_ios),
            ),
          ),
          Card(
            child: ListTile(
              leading: const CircleAvatar(
                backgroundImage: NetworkImage('https://via.placeholder.com/50?text=Marie'),
              ),
              title: const Text('Marie Laurent'),
              subtitle: const Text('Design Expert'),
              trailing: const Icon(Icons.arrow_forward_ios),
            ),
          ),
          Card(
            child: ListTile(
              leading: const CircleAvatar(
                backgroundImage: NetworkImage('https://via.placeholder.com/50?text=Chloe'),
              ),
              title: const Text('Chloé Martin'),
              subtitle: const Text('Cuisine Expert'),
              trailing: const Icon(Icons.arrow_forward_ios),
            ),
          ),
        ],
      ),
    );
  }

  // Méthode pour créer un élément de navigation circulaire
  Widget _buildCircularNavItem({
    required IconData icon,
    required String label,
    required int index,
    required VoidCallback onTap,
  }) {
    bool isSelected = _currentIndex == index;
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isSelected ? Colors.pink[100] : Colors.transparent,
              border: Border.all(
                color: isSelected ? Colors.pink[400]! : Colors.grey[300]!,
                width: 2,
              ),
            ),
            child: Icon(
              icon,
              color: isSelected ? Colors.pink[600] : Colors.grey[600],
              size: 24,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: isSelected ? Colors.pink[600] : Colors.grey[600],
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getAppBarTitle()),
        backgroundColor: Colors.pink[50],
        elevation: 0,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const ProfilScreen()),
                );
              },
              child: CircleAvatar(
                backgroundImage: NetworkImage('https://via.placeholder.com/40?text=User'),
              ),
            ),
          ),
        ],
      ),
      body: _getPageContent(),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.3),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildCircularNavItem(
              icon: Icons.home_rounded,
              label: 'Accueil',
              index: 0,
              onTap: () {
                setState(() {
                  _currentIndex = 0;
                });
              },
            ),
            _buildCircularNavItem(
              icon: Icons.explore_rounded,
              label: 'Parcourir',
              index: 1,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const ParcourirScreen()),
                );
              },
            ),
            _buildCircularNavItem(
              icon: Icons.school_rounded,
              label: 'Mes Cours',
              index: 2,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const MesCoursScreen()),
                );
              },
            ),
            _buildCircularNavItem(
              icon: Icons.receipt_long_rounded,
              label: 'Instructions',
              index: 3,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const InstructionsScreen()),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}