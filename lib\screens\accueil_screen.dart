import 'package:flutter/material.dart';
import 'parcourir_screen.dart';
import 'mes_cours_screen.dart';
import 'instructions_screen.dart';
import 'profil_screen.dart';
import '../widgets/custom_icons.dart';

class AccueilScreen extends StatefulWidget {
  const AccueilScreen({super.key});

  @override
  State<AccueilScreen> createState() => _AccueilScreenState();
}

class _AccueilScreenState extends State<AccueilScreen> {
  int _currentIndex = 0;

  // Méthode pour obtenir le titre de l'AppBar selon l'index sélectionné
  String _getAppBarTitle() {
    switch (_currentIndex) {
      case 0:
        return 'Bienvenue';
      case 1:
        return 'Parcourir';
      case 2:
        return 'Mes Cours';
      case 3:
        return 'Instructions';
      default:
        return 'Bienvenue';
    }
  }

  // Contenu de la page d'accueil uniquement
  Widget _getPageContent() {
    return _buildAccueilContent();
  }

  // Contenu de la page d'accueil
  Widget _buildAccueilContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Card(
        color: Colors.pink[50],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Bonjour, Sarah !',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'Prête à devenir une experte en pâtisserie ?',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Image.asset('assets/images/R.jpg', height: 200, fit: BoxFit.cover),
              const SizedBox(height: 16),
              const Text(
                'Nouveaux Cours de Pâtisserie',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'Créative !',
                style: TextStyle(fontSize: 16, fontStyle: FontStyle.italic),
              ),
              const SizedBox(height: 8),
              const Text(
                'Découvrez l\'art de la pâtisserie dès aujourd\'hui avec des bases avancées et des techniques créatives, avec notre équipe renommée.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const MesCoursScreen()),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.pink[100],
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: const Text('Explorer les Cours de Pâtisserie'),
              ),
            ],
          ),
        ),
      ),
    );
  }



  // Méthode pour créer un élément de navigation avec icônes dessinées
  Widget _buildCustomNavItem({
    required Widget customIcon,
    required String label,
    required int index,
    required VoidCallback onTap,
  }) {
    bool isSelected = _currentIndex == index;
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: isSelected
                ? LinearGradient(
                    colors: [Colors.pink[200]!, Colors.pink[400]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : LinearGradient(
                    colors: [Colors.grey[100]!, Colors.grey[200]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
              boxShadow: [
                BoxShadow(
                  color: isSelected
                    ? Colors.pink.withValues(alpha: 0.4)
                    : Colors.grey.withValues(alpha: 0.2),
                  spreadRadius: 2,
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
              border: Border.all(
                color: isSelected ? Colors.pink[600]! : Colors.grey[400]!,
                width: 3,
              ),
            ),
            child: customIcon,
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: isSelected ? Colors.pink[100] : Colors.transparent,
            ),
            child: Text(
              label,
              style: TextStyle(
                fontSize: 11,
                color: isSelected ? Colors.pink[800] : Colors.grey[600],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getAppBarTitle()),
        backgroundColor: Colors.pink[50],
        elevation: 0,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const ProfilScreen()),
                );
              },
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [Colors.pink[300]!, Colors.pink[500]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.pink.withValues(alpha: 0.4),
                      spreadRadius: 2,
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ],
                  border: Border.all(
                    color: Colors.white,
                    width: 3,
                  ),
                ),
                child: CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.transparent,
                  child: Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: _getPageContent(),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.3),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildCustomNavItem(
              customIcon: CustomIcons.homeIcon(
                color: _currentIndex == 0 ? Colors.white : Colors.grey[700],
                size: 28,
              ),
              label: 'Accueil',
              index: 0,
              onTap: () {
                setState(() {
                  _currentIndex = 0;
                });
              },
            ),
            _buildCustomNavItem(
              customIcon: CustomIcons.searchIcon(
                color: _currentIndex == 1 ? Colors.white : Colors.grey[700],
                size: 28,
              ),
              label: 'Parcourir',
              index: 1,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const ParcourirScreen()),
                );
              },
            ),
            _buildCustomNavItem(
              customIcon: CustomIcons.bookIcon(
                color: _currentIndex == 2 ? Colors.white : Colors.grey[700],
                size: 28,
              ),
              label: 'Mes Cours',
              index: 2,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const MesCoursScreen()),
                );
              },
            ),
            _buildCustomNavItem(
              customIcon: CustomIcons.personIcon(
                color: _currentIndex == 3 ? Colors.white : Colors.grey[700],
                size: 28,
              ),
              label: 'Instructions',
              index: 3,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const InstructionsScreen()),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}