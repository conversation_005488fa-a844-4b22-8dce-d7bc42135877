import 'package:flutter/material.dart';

class ProfilScreen extends StatelessWidget {
  const ProfilScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mon Profil'),
        backgroundColor: Colors.pink[50],
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const CircleAvatar(
              radius: 50,
              backgroundImage: NetworkImage('https://via.placeholder.com/100?text=User'),
            ),
            const SizedBox(height: 16),
            const Text('<PERSON>', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            const SizedBox(height: 24),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    TextField(
                      decoration: const InputDecoration(labelText: 'Nom'),
                      controller: TextEditingController(text: '<PERSON>'),
                    ),
                    TextField(
                      decoration: const InputDecoration(labelText: 'Email'),
                      controller: TextEditingController(text: '<EMAIL>'),
                    ),
                    TextField(
                      decoration: const InputDecoration(labelText: 'Téléphone'),
                      controller: TextEditingController(text: '+33 6 12 34 56 78'),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {},
                      child: const Text('Sauvegarder'),
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[100]),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}