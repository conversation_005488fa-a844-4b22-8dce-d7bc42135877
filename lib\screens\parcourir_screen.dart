import 'package:flutter/material.dart';

class ParcourirScreen extends StatelessWidget {
  const ParcourirScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Parcourir les cours'),
        backgroundColor: Colors.pink[50],
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Card(
              child: ListTile(
                leading: const Icon(Icons.category, color: Colors.pink),
                title: const Text('Catégories de cours'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {},
              ),
            ),
            Card(
              child: ListTile(
                leading: Image.asset('assets/cooking.jpg', width: 60, fit: BoxFit.cover), // Remplacez par votre image
                title: const Text('Maîtrise de la Pâtisserie'),
                subtitle: const Text('Niveau Débutant'),
                trailing: const Icon(Icons.arrow_forward_ios),
              ),
            ),
            Card(
              child: ListTile(
                leading: Image.asset('assets/classroom.jpg', width: 60, fit: BoxFit.cover), // Remplacez par votre image
                title: const Text('Techniques de Décoration'),
                subtitle: const Text('Intermédiaire'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}