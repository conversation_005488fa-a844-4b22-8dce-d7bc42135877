import 'package:flutter/material.dart';

class ParcourirScreen extends StatelessWidget {
  const ParcourirScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Parcourir les cours'),
        backgroundColor: Colors.pink[50],
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Catégories',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('Tous les catégories'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.pink[50],
                    side: BorderSide(color: Colors.pink[100]!),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('Cuisine'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[50]),
                ),
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('Couture'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[50]),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Niveaux',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('Tous les niveaux'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.pink[50],
                    side: BorderSide(color: Colors.pink[100]!),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('Débutant'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[50]),
                ),
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('Intermédiaire'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.pink[50]),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Explorez les cours',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            Card(
              child: ListTile(
                leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
                title: const Text('Maîtrise de la Pâtisserie'),
                subtitle: const Text('Chef Amélie Dul'),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Text('Avancé'),
                    LinearProgressIndicator(value: 0.75),
                    Text('75% terminé'),
                  ],
                ),
              ),
            ),
            Card(
              child: ListTile(
                leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
                title: const Text('Techniques de Maquillage'),
                subtitle: const Text('Léa Bernard'),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Text('Intermédiaire'),
                    LinearProgressIndicator(value: 0.50),
                    Text('50% terminé'),
                  ],
                ),
              ),
            ),
            Card(
              child: ListTile(
                leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
                title: const Text('Initiation à la Couture'),
                subtitle: const Text('Sophie Martin'),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Text('Débutant'),
                    LinearProgressIndicator(value: 0.25),
                    Text('25% terminé'),
                  ],
                ),
              ),
            ),
            Card(
              child: ListTile(
                leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
                title: const Text('Anglais des Affaires :'),
                subtitle: const Text('Mme. Emily Wat'),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Text('Intermédiaire'),
                    LinearProgressIndicator(value: 0.30),
                    Text('30% terminé'),
                  ],
                ),
              ),
            ),
            Card(
              child: ListTile(
                leading: Image.asset('assets/images/R.jpg', width: 60, fit: BoxFit.cover),
                title: const Text('Entrepreneuriat'),
                subtitle: const Text('Les Fondamen'),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Text('Dr. Claire Laur'),
                    LinearProgressIndicator(value: 0.10),
                    Text('10% terminé'),
                  ],
                ),
              ),
            ),
            Card(
              child: ListTile(
                leading: Image.asset('assets/images/A.jpg', width: 60, fit: BoxFit.cover),
                title: const Text('Introduction au Design'),
                subtitle: const Text('Sarah Dupont'),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Text('Avancé'),
                    LinearProgressIndicator(value: 0.15),
                    Text('15% terminé'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}