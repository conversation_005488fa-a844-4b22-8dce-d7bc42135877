import 'package:flutter/material.dart';

class MesCoursScreen extends StatelessWidget {
  const MesCoursScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mes Cours'),
        backgroundColor: Colors.pink[50],
        elevation: 0,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: CircleAvatar(
              backgroundImage: NetworkImage('https://via.placeholder.com/40?text=User'), // Remplacez par l'image réelle
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Card(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Image.asset('assets/images/cake.jpg', height: 150, fit: BoxFit.cover),
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: <PERSON>umn(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Maîtriser la Pâtisserie Française : Les Essentiels',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        const Text(
                          'Chef Sophie Dubois',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(value: 0.75),
                        const SizedBox(height: 4),
                        const Text('75%'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Image.asset('assets/images/sewing.jpg', height: 150, fit: BoxFit.cover),
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Confectionner des Vêtements Élégants : Niveaux Débutant',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        const Text(
                          'Styliste Clara Martin',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(value: 0.50),
                        const SizedBox(height: 4),
                        const Text('50%'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        backgroundColor: Colors.white,
        selectedItemColor: Colors.pink[400],
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Accueil'),
          BottomNavigationBarItem(icon: Icon(Icons.search), label: 'Parcourir'),
          BottomNavigationBarItem(icon: Icon(Icons.book), label: 'Mes Cours'),
          BottomNavigationBarItem(icon: Icon(Icons.menu_book), label: 'Instructions'),
        ],
      ),
    );
  }
}