import 'package:flutter/material.dart';

class MesCoursScreen extends StatelessWidget {
  const MesCoursScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mes cours'),
        backgroundColor: Colors.pink[50],
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Card(
              child: Column(
                children: [
                  Image.asset('assets/cake.jpg', height: 150, fit: BoxFit.cover), // Remplacez par votre image
                  const Padding(
                    padding: EdgeInsets.all(16),
                    child: Text(
                      'Maîtrise de la Pâtisserie Française',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ),
                  LinearProgressIndicator(value: 0.25),
                  const SizedBox(height: 8),
                  const Text('25% complété'),
                ],
              ),
            ),
            Card(
              child: Column(
                children: [
                  Image.asset('assets/sewing.jpg', height: 150, fit: BoxFit.cover), // Remplacez par votre image
                  const Padding(
                    padding: EdgeInsets.all(16),
                    child: Text(
                      'Couture pour Débutants et Intermédiaires',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ),
                  LinearProgressIndicator(value: 0.10),
                  const SizedBox(height: 8),
                  const Text('10% complété'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}