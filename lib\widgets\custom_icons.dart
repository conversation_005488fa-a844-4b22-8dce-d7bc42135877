import 'package:flutter/material.dart';

class CustomIcons {
  // Icône maison dessinée
  static Widget homeIcon({Color? color, double size = 24}) {
    return CustomPaint(
      size: <PERSON><PERSON>(size, size),
      painter: HomeIconPainter(color: color ?? Colors.grey),
    );
  }

  // Icône loupe dessinée
  static Widget searchIcon({Color? color, double size = 24}) {
    return CustomPaint(
      size: <PERSON><PERSON>(size, size),
      painter: SearchIconPainter(color: color ?? Colors.grey),
    );
  }

  // Icône livre dessiné
  static Widget bookIcon({Color? color, double size = 24}) {
    return CustomPaint(
      size: Size(size, size),
      painter: BookIconPainter(color: color ?? Colors.grey),
    );
  }

  // Icône personne dessinée
  static Widget personIcon({Color? color, double size = 24}) {
    return CustomPaint(
      size: Size(size, size),
      painter: PersonIconPainter(color: color ?? Colors.grey),
    );
  }
}

// Peintre pour l'icône maison
class HomeIconPainter extends CustomPainter {
  final Color color;

  HomeIconPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.5
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final path = Path();
    
    // Toit de la maison
    path.moveTo(size.width * 0.5, size.height * 0.1);
    path.lineTo(size.width * 0.1, size.height * 0.5);
    path.lineTo(size.width * 0.9, size.height * 0.5);
    path.close();
    
    // Corps de la maison
    path.moveTo(size.width * 0.2, size.height * 0.5);
    path.lineTo(size.width * 0.2, size.height * 0.9);
    path.lineTo(size.width * 0.8, size.height * 0.9);
    path.lineTo(size.width * 0.8, size.height * 0.5);
    
    // Porte
    path.moveTo(size.width * 0.45, size.height * 0.9);
    path.lineTo(size.width * 0.45, size.height * 0.65);
    path.lineTo(size.width * 0.55, size.height * 0.65);
    path.lineTo(size.width * 0.55, size.height * 0.9);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Peintre pour l'icône loupe
class SearchIconPainter extends CustomPainter {
  final Color color;

  SearchIconPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.5
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Cercle de la loupe
    canvas.drawCircle(
      Offset(size.width * 0.4, size.height * 0.4),
      size.width * 0.25,
      paint,
    );

    // Manche de la loupe
    canvas.drawLine(
      Offset(size.width * 0.6, size.height * 0.6),
      Offset(size.width * 0.85, size.height * 0.85),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Peintre pour l'icône livre
class BookIconPainter extends CustomPainter {
  final Color color;

  BookIconPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.5
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final path = Path();
    
    // Couverture du livre
    path.moveTo(size.width * 0.2, size.height * 0.1);
    path.lineTo(size.width * 0.8, size.height * 0.1);
    path.lineTo(size.width * 0.8, size.height * 0.9);
    path.lineTo(size.width * 0.2, size.height * 0.9);
    path.close();
    
    // Reliure
    path.moveTo(size.width * 0.2, size.height * 0.1);
    path.lineTo(size.width * 0.2, size.height * 0.9);
    
    // Pages
    path.moveTo(size.width * 0.3, size.height * 0.3);
    path.lineTo(size.width * 0.7, size.height * 0.3);
    path.moveTo(size.width * 0.3, size.height * 0.5);
    path.lineTo(size.width * 0.7, size.height * 0.5);
    path.moveTo(size.width * 0.3, size.height * 0.7);
    path.lineTo(size.width * 0.6, size.height * 0.7);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Peintre pour l'icône personne
class PersonIconPainter extends CustomPainter {
  final Color color;

  PersonIconPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.5
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Tête
    canvas.drawCircle(
      Offset(size.width * 0.5, size.height * 0.3),
      size.width * 0.15,
      paint,
    );

    // Corps
    final path = Path();
    path.moveTo(size.width * 0.5, size.height * 0.45);
    path.lineTo(size.width * 0.5, size.height * 0.75);
    
    // Épaules
    path.moveTo(size.width * 0.25, size.height * 0.9);
    path.quadraticBezierTo(
      size.width * 0.5, size.height * 0.6,
      size.width * 0.75, size.height * 0.9,
    );

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
